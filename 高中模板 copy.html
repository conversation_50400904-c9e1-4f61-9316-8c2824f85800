<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Unofficial Transcript - Chandler High School</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Source+Sans+3:wght@400;600;700&family=Playfair+Display:wght@700&display=swap');

    :root {
      --primary: #1B365D;
      --secondary: #FFD700;
      --light-gray: #f5f5f5;
      --medium-gray: #e0e0e0;
      --dark-gray: #555;
      --border-radius: 6px;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Source Sans 3', sans-serif;
      font-size: 11px;
      line-height: 1.5;
      color: #333;
      background-color: #f0f0f0;
      padding: 20px;
    }

    .container {
      max-width: 210mm;
      margin: 0 auto;
      background: white url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" opacity="0.03"><text x="0" y="40" font-family="Arial" font-size="20" fill="black" transform="rotate(45 100,100)">UNOFFICIAL TRANSCRIPT</text></svg>');
      box-shadow: 0 0 20px rgba(0,0,0,0.1);
      position: relative;
      border-radius: var(--border-radius);
      overflow: hidden;
    }

    .header {
      display: grid;
      grid-template-columns: auto 1fr auto;
      align-items: center;
      padding: 15px 25px;
      background-color: var(--primary);
      color: white;
      border-bottom: 3px solid var(--secondary);
    }

    .logo {
      display: flex;
      flex-direction: column;
      gap: 5px;
    }

    .school-name {
      font-family: 'Playfair Display', serif;
      font-size: 24px;
      font-weight: 700;
      letter-spacing: 0.5px;
    }

    .school-info {
      font-size: 10px;
      line-height: 1.3;
    }

    .document-title {
      text-align: right;
      font-size: 18px;
      font-weight: 700;
      padding-right: 20px;
    }

    .print-info {
      text-align: right;
      font-size: 10px;
      padding: 8px 25px;
      background-color: var(--light-gray);
      border-bottom: 1px solid var(--medium-gray);
    }

    .student-section {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      padding: 20px 25px;
      border-bottom: 1px solid var(--medium-gray);
    }

    .section-title {
      color: var(--primary);
      font-weight: 700;
      text-transform: uppercase;
      font-size: 14px;
      margin-bottom: 10px;
      font-family: 'Playfair Display', serif;
      letter-spacing: 0.5px;
      border-bottom: 2px solid var(--secondary);
      padding-bottom: 5px;
      display: inline-block;
    }

    .student-info {
      display: grid;
      grid-template-columns: auto 1fr;
      gap: 8px 15px;
    }

    .info-label {
      font-weight: 600;
      color: var(--dark-gray);
    }

    .summary-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;
    }

    .summary-item {
      display: flex;
      justify-content: space-between;
      padding: 5px 10px;
      background-color: var(--light-gray);
      border-radius: 4px;
    }

    .summary-item:nth-child(odd) {
      background-color: var(--medium-gray);
    }

    .summary-label {
      font-weight: 600;
      color: var(--dark-gray);
    }

    .academic-record {
      padding: 20px 25px;
    }

    .semester {
      margin-bottom: 25px;
    }

    .semester-header {
      background-color: var(--primary);
      color: white;
      padding: 8px 15px;
      font-weight: 600;
      border-radius: var(--border-radius) var(--border-radius) 0 0;
      display: flex;
      justify-content: space-between;
    }

    .courses-table {
      width: 100%;
      border-collapse: collapse;
      border: 1px solid var(--medium-gray);
      border-top: none;
    }

    .courses-table th {
      background-color: var(--light-gray);
      font-weight: 600;
      text-align: left;
      padding: 8px 15px;
      border-bottom: 1px solid var(--medium-gray);
    }

    .courses-table td {
      padding: 6px 15px;
      border-bottom: 1px solid var(--medium-gray);
    }

    .courses-table tr:nth-child(even) {
      background-color: var(--light-gray);
    }

    .course-code {
      font-weight: 600;
      color: var(--primary);
    }

    .semester-summary {
      display: flex;
      justify-content: space-between;
      padding: 8px 15px;
      background-color: var(--light-gray);
      border: 1px solid var(--medium-gray);
      border-top: none;
      border-radius: 0 0 var(--border-radius) var(--border-radius);
      font-weight: 600;
    }

    .ip-course {
      font-style: italic;
      color: var(--secondary);
      font-weight: 600;
    }

    .footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 25px;
      background-color: var(--primary);
      color: white;
      font-size: 10px;
      border-top: 3px solid var(--secondary);
      margin-top: 20px;
    }

    .signature-area {
      display: flex;
      justify-content: space-between;
      padding: 15px 25px;
      border-top: 1px dashed var(--medium-gray);
    }

    .signature {
      text-align: center;
    }

    .signature-line {
      width: 150px;
      border-top: 1px solid black;
      margin: 40px auto 5px;
    }

    .watermark {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) rotate(-45deg);
      font-size: 100px;
      font-weight: 700;
      color: rgba(0,0,0,0.03);
      white-space: nowrap;
      pointer-events: none;
      z-index: 10;
    }

    @media print {
      body {
        padding: 0;
        background: none;
      }
      
      .container {
        box-shadow: none;
        border-radius: 0;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="watermark">UNOFFICIAL</div>
    
    <header class="header">
      <div class="logo">
        <div class="school-name">Chandler High School</div>
        <div class="school-info">
          350 N. Arizona Ave<br>
          Chandler, AZ 85225<br>
          (480) 812-7000
        </div>
      </div>
      <div></div>
      <div class="document-title"></div>
    </header>
    
    <div class="print-info">
      <div><strong>Print Date:</strong> 06/02/2025 | <strong>CEEB Code:</strong> 030425 | <strong>Unofficial Transcript</strong></div>
    </div>
    
    <section class="student-section">
      <div>
        <div class="section-title">Student Information</div>
        <div class="student-info">
          <div class="info-label">Name:</div>
          <div>Evelyn Rogers</div>
          
          <div class="info-label">Student ID:</div>
          <div>1202485139</div>
          
          <div class="info-label">Date of Birth:</div>
          <div>July 1, 2004</div>
          
          <div class="info-label">Graduation Date:</div>
          <div>June 15, 2024</div>
          
          <div class="info-label">Address:</div>
          <div>2847 E. Apache Blvd, Tempe, AZ 85281</div>
          
          <div class="info-label">Diploma:</div>
          <div>High School Diploma - College Preparatory</div>
        </div>
      </div>
      
      <div>
        <div class="section-title">Academic Summary</div>
        <div class="summary-grid">
          <div class="summary-item">
            <span class="summary-label">Cumulative GPA:</span>
            <span>3.45</span>
          </div>
          
          <div class="summary-item">
            <span class="summary-label">Class Rank:</span>
            <span>85 of 425</span>
          </div>
          
          <div class="summary-item">
            <span class="summary-label">Total Credits:</span>
            <span>26.0</span>
          </div>
          
          <div class="summary-item">
            <span class="summary-label">Honors/Awards:</span>
            <span>Honor Roll</span>
          </div>
          
          <div class="summary-item">
            <span class="summary-label">Graduation Status:</span>
            <span>Graduated</span>
          </div>
          
          <div class="summary-item">
            <span class="summary-label">Years Attended:</span>
            <span>2020-2024</span>
          </div>
        </div>
      </div>
    </section>
    
    <section class="academic-record">
      <div class="section-title">Academic Record</div>
      
      <div class="semester">
        <div class="semester-header">
          <span>Freshman Year (2020-2021)</span>
          <span>Year GPA: 3.40</span>
        </div>
        <table class="courses-table">
          <tr>
            <th width="15%">Course ID</th>
            <th width="50%">Course Title</th>
            <th width="15%">Grade</th>
            <th width="20%">Credit Hours</th>
          </tr>
          <tr>
            <td class="course-code">ENG101</td>
            <td>English 9</td>
            <td>A-</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">ALG201</td>
            <td>Algebra I</td>
            <td>B+</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">BIO101</td>
            <td>Biology</td>
            <td>A</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">WSH101</td>
            <td>World History</td>
            <td>A-</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">SPN101</td>
            <td>Spanish I</td>
            <td>B+</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">PE101</td>
            <td>Physical Education</td>
            <td>A</td>
            <td>0.5</td>
          </tr>
          <tr>
            <td class="course-code">ART101</td>
            <td>Art Foundations</td>
            <td>A</td>
            <td>0.5</td>
          </tr>
        </table>
        <div class="semester-summary">
          <span>Total Credits: 6.0</span>
          <span>Year GPA: 3.40</span>
        </div>
      </div>
      
      <div class="semester">
        <div class="semester-header">
          <span>Sophomore Year (2021-2022)</span>
          <span>Year GPA: 3.45</span>
        </div>
        <table class="courses-table">
          <tr>
            <th width="15%">Course ID</th>
            <th width="50%">Course Title</th>
            <th width="15%">Grade</th>
            <th width="20%">Credit Hours</th>
          </tr>
          <tr>
            <td class="course-code">ENG102</td>
            <td>English 10</td>
            <td>A</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">GEO201</td>
            <td>Geometry</td>
            <td>B+</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">CHM101</td>
            <td>Chemistry</td>
            <td>A-</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">USH101</td>
            <td>U.S. History</td>
            <td>A</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">SPN102</td>
            <td>Spanish II</td>
            <td>A-</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">PE102</td>
            <td>Physical Education</td>
            <td>A</td>
            <td>0.5</td>
          </tr>
          <tr>
            <td class="course-code">MUS101</td>
            <td>Concert Band</td>
            <td>A</td>
            <td>0.5</td>
          </tr>
        </table>
        <div class="semester-summary">
          <span>Total Credits: 6.0</span>
          <span>Year GPA: 3.45</span>
        </div>
      </div>

      <div class="semester">
        <div class="semester-header">
          <span>Junior Year (2022-2023)</span>
          <span>Year GPA: 3.50</span>
        </div>
        <table class="courses-table">
          <tr>
            <th width="15%">Course ID</th>
            <th width="50%">Course Title</th>
            <th width="15%">Grade</th>
            <th width="20%">Credit Hours</th>
          </tr>
          <tr>
            <td class="course-code">ENG103</td>
            <td>English 11</td>
            <td>A</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">ALG301</td>
            <td>Algebra II</td>
            <td>B+</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">PHY101</td>
            <td>Physics</td>
            <td>A-</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">GOV101</td>
            <td>Government & Economics</td>
            <td>A</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">SPN103</td>
            <td>Spanish III</td>
            <td>A</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">HLT101</td>
            <td>Health Education</td>
            <td>A</td>
            <td>0.5</td>
          </tr>
          <tr>
            <td class="course-code">ELC101</td>
            <td>Computer Applications</td>
            <td>A</td>
            <td>0.5</td>
          </tr>
        </table>
        <div class="semester-summary">
          <span>Total Credits: 6.0</span>
          <span>Year GPA: 3.50</span>
        </div>
      </div>

      <div class="semester">
        <div class="semester-header">
          <span>Senior Year (2023-2024)</span>
          <span>Year GPA: 3.45</span>
        </div>
        <table class="courses-table">
          <tr>
            <th width="15%">Course ID</th>
            <th width="50%">Course Title</th>
            <th width="15%">Grade</th>
            <th width="20%">Credit Hours</th>
          </tr>
          <tr>
            <td class="course-code">ENG104</td>
            <td>English 12</td>
            <td>A-</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">PRE101</td>
            <td>Pre-Calculus</td>
            <td>B</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">ENV101</td>
            <td>Environmental Science</td>
            <td>A</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">PSY101</td>
            <td>Psychology</td>
            <td>A</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">SPN104</td>
            <td>Spanish IV</td>
            <td>A-</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">ART201</td>
            <td>Advanced Art</td>
            <td>A</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">SEM101</td>
            <td>Senior Seminar</td>
            <td>A</td>
            <td>2.0</td>
          </tr>
        </table>
        <div class="semester-summary">
          <span>Total Credits: 8.0</span>
          <span>Year GPA: 3.45</span>
        </div>
      </div>
    </section>

    <footer class="footer">
      <div>© 2024 Chandler High School. All rights reserved.</div>
      <div>This unofficial transcript contains 26.0 credits required for graduation.</div>
    </footer>
  </div>
</body>
</html>
