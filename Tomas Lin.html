<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Official Academic Transcript - Purdue University</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: Arial, sans-serif;
      font-size: 14px;
      line-height: 1.4;
      background: white;
      padding: 20px;
      color: #333;
    }
    
    .transcript-container {
      max-width: 850px;
      margin: 0 auto;
      background: white;
      border: 1px solid #ccc;
    }
    
    .header {
      background: #333;
      color: white;
      padding: 25px;
      text-align: center;
    }
    
    .university-name {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 5px;
    }
    
    .university-subtitle {
      font-size: 14px;
      margin-bottom: 15px;
    }
    
    .transcript-type {
      font-size: 13px;
      font-weight: bold;
    }
    
    .content {
      padding: 30px;
    }
    
    .document-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 25px;
      padding-bottom: 15px;
      border-bottom: 1px solid #333;
    }
    
    .doc-title {
      font-size: 20px;
      font-weight: bold;
    }
    
    .print-date {
      font-size: 12px;
      text-align: right;
    }
    
    .student-section {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 40px;
      margin-bottom: 25px;
    }
    
    .info-card {
      border: 1px solid #ddd;
      padding: 20px;
    }
    
    .card-title {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 15px;
      text-transform: uppercase;
      border-bottom: 1px solid #ddd;
      padding-bottom: 5px;
    }
    
    .info-item {
      display: flex;
      margin-bottom: 8px;
    }
    
    .info-label {
      font-weight: bold;
      width: 110px;
      flex-shrink: 0;
    }
    
    .info-value {
      color: #333;
    }
    
    .academic-summary {
      background: #f8f8f8;
      padding: 20px;
      margin-bottom: 25px;
      border: 1px solid #ddd;
    }
    
    .summary-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      text-align: center;
    }
    
    .summary-table {
      width: 100%;
      border-collapse: collapse;
      margin: 0;
    }
    
    .summary-table td {
      padding: 10px 15px;
      text-align: center;
      border-right: 1px solid #ddd;
      vertical-align: top;
    }
    
    .summary-table td:last-child {
      border-right: none;
    }
    
    .summary-label {
      font-size: 11px;
      text-transform: uppercase;
      color: #666;
      margin-bottom: 5px;
      font-weight: bold;
    }
    
    .summary-value {
      font-size: 18px;
      font-weight: bold;
      color: #333;
    }
    
    .term-section {
      margin-bottom: 20px;
      border: 1px solid #ddd;
    }
    
    .term-header {
      background: #f0f0f0;
      padding: 12px 20px;
      font-weight: bold;
      font-size: 14px;
      border-bottom: 1px solid #ddd;
    }
    
    .courses-table {
      width: 100%;
      border-collapse: collapse;
    }
    
    .courses-table th {
      background: #f8f8f8;
      padding: 10px;
      text-align: left;
      font-weight: bold;
      font-size: 12px;
      border-bottom: 1px solid #ddd;
    }
    
    .courses-table td {
      padding: 10px;
      border-bottom: 1px solid #eee;
      font-size: 13px;
    }
    
    .courses-table tr:last-child td {
      border-bottom: none;
    }
    
    .course-code {
      font-weight: bold;
    }
    
    .course-grade {
      text-align: center;
      font-weight: bold;
    }
    
    .course-credits {
      text-align: center;
    }
    
    .term-summary {
      background: #f0f0f0;
      padding: 12px 20px;
      text-align: center;
      font-weight: bold;
      font-size: 13px;
      border-top: 1px solid #ddd;
    }
    
    .current-term {
      border: 2px solid #333;
    }
    
    .current-term .term-header {
      background: #333;
      color: white;
    }
    
    @media print {
      body { 
        padding: 0; 
      }
    }
    
    @media (max-width: 768px) {
      .student-section {
        grid-template-columns: 1fr;
        gap: 20px;
      }
      
      .summary-table {
        font-size: 12px;
      }
      
      .summary-table td {
        padding: 8px 5px;
      }
      
      .summary-value {
        font-size: 16px;
      }
      
      .courses-table, .courses-table thead, .courses-table tbody, .courses-table th, .courses-table td, .courses-table tr {
        display: block;
      }
      
      .courses-table thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
      }
      
      .courses-table tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        padding: 10px;
      }
      
      .courses-table td {
        border: none;
        position: relative;
        padding-left: 50%;
      }
      
      .courses-table td:before {
        content: attr(data-label) ": ";
        position: absolute;
        left: 6px;
        width: 45%;
        padding-right: 10px;
        white-space: nowrap;
        font-weight: bold;
      }
    }
  </style>
</head>
<body>
  <div class="transcript-container">
    <div class="header">
      <h1 class="university-name">Purdue University</h1>
      <p class="university-subtitle">West Lafayette, IN 47907</p>
      <div class="transcript-type">UNOFFICIAL ACADEMIC TRANSCRIPT</div>
    </div>
    
    <div class="content">
      <div class="document-info">
        <h2 class="doc-title">Academic Record</h2>
        <div class="print-date">
          <div>Issue Date: June 15, 2025</div>
          <div>Document ID: PU-TR-2025-06-1247</div>
        </div>
      </div>
      
      <div class="student-section">
        <div class="info-card">
          <h3 class="card-title">Student Information</h3>
          <div class="info-item">
            <span class="info-label">Name:</span>
            <span class="info-value">Tomas Lin</span>
          </div>
          <div class="info-item">
            <span class="info-label">Student ID:</span>
            <span class="info-value">0032451789</span>
          </div>
          <div class="info-item">
            <span class="info-label">Date of Birth:</span>
            <span class="info-value">August 26, 2005</span>
          </div>
          <div class="info-item">
            <span class="info-label">Address:</span>
            <span class="info-value">425 Stadium Mall Dr, West Lafayette, IN 47907</span>
          </div>
        </div>
        
        <div class="info-card">
          <h3 class="card-title">Academic Program</h3>
          <div class="info-item">
            <span class="info-label">College:</span>
            <span class="info-value">College of Science</span>
          </div>
          <div class="info-item">
            <span class="info-label">Major:</span>
            <span class="info-value">Computer Science</span>
          </div>
          <div class="info-item">
            <span class="info-label">Minor:</span>
            <span class="info-value">Mathematics</span>
          </div>
          <div class="info-item">
            <span class="info-label">Degree:</span>
            <span class="info-value">Bachelor of Science</span>
          </div>
          <div class="info-item">
            <span class="info-label">Expected Grad:</span>
            <span class="info-value">May 2027</span>
          </div>
        </div>
      </div>
      
      <div class="academic-summary">
        <h3 class="summary-title">Academic Performance Summary</h3>
        <table class="summary-table">
          <tbody>
            <tr>
              <td>
                <div class="summary-label">Cumulative GPA</div>
                <div class="summary-value">3.65</div>
              </td>
              <td>
                <div class="summary-label">Credits Earned</div>
                <div class="summary-value">64</div>
              </td>
              <td>
                <div class="summary-label">Credits in Progress</div>
                <div class="summary-value">16</div>
              </td>
              <td>
                <div class="summary-label">Class Standing</div>
                <div class="summary-value">Sophomore</div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <div class="term-section">
        <div class="term-header">Fall Semester 2023 - First Year</div>
        <table class="courses-table">
          <thead>
            <tr>
              <th>Course Code</th>
              <th>Course Title</th>
              <th>Grade</th>
              <th>Credits</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="course-code" data-label="Course Code">CS 18000</td>
              <td data-label="Course Title">Problem Solving and Object-Oriented Programming</td>
              <td class="course-grade" data-label="Grade">A-</td>
              <td class="course-credits" data-label="Credits">4.0</td>
            </tr>
            <tr>
              <td class="course-code" data-label="Course Code">MA 16100</td>
              <td data-label="Course Title">Plane Analytic Geometry and Calculus I</td>
              <td class="course-grade" data-label="Grade">B+</td>
              <td class="course-credits" data-label="Credits">5.0</td>
            </tr>
            <tr>
              <td class="course-code" data-label="Course Code">ENGL 10600</td>
              <td data-label="Course Title">First-Year Composition</td>
              <td class="course-grade" data-label="Grade">A</td>
              <td class="course-credits" data-label="Credits">4.0</td>
            </tr>
            <tr>
              <td class="course-code" data-label="Course Code">PHYS 17200</td>
              <td data-label="Course Title">Modern Mechanics</td>
              <td class="course-grade" data-label="Grade">B</td>
              <td class="course-credits" data-label="Credits">3.0</td>
            </tr>
          </tbody>
        </table>
        <div class="term-summary">Term GPA: 3.56 • Total Credits: 16.0 • Cumulative GPA: 3.56</div>
      </div>
      
      <div class="term-section">
        <div class="term-header">Spring Semester 2024 - First Year</div>
        <table class="courses-table">
          <thead>
            <tr>
              <th>Course Code</th>
              <th>Course Title</th>
              <th>Grade</th>
              <th>Credits</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="course-code" data-label="Course Code">CS 18200</td>
              <td data-label="Course Title">Foundations of Computer Science</td>
              <td class="course-grade" data-label="Grade">A</td>
              <td class="course-credits" data-label="Credits">3.0</td>
            </tr>
            <tr>
              <td class="course-code" data-label="Course Code">MA 16200</td>
              <td data-label="Course Title">Plane Analytic Geometry and Calculus II</td>
              <td class="course-grade" data-label="Grade">A-</td>
              <td class="course-credits" data-label="Credits">5.0</td>
            </tr>
            <tr>
              <td class="course-code" data-label="Course Code">PHYS 27200</td>
              <td data-label="Course Title">Electric and Magnetic Interactions</td>
              <td class="course-grade" data-label="Grade">B+</td>
              <td class="course-credits" data-label="Credits">3.0</td>
            </tr>
            <tr>
              <td class="course-code" data-label="Course Code">COMM 11400</td>
              <td data-label="Course Title">Fundamentals of Speech Communication</td>
              <td class="course-grade" data-label="Grade">A</td>
              <td class="course-credits" data-label="Credits">3.0</td>
            </tr>
            <tr>
              <td class="course-code" data-label="Course Code">SOC 10000</td>
              <td data-label="Course Title">Introductory Sociology</td>
              <td class="course-grade" data-label="Grade">B+</td>
              <td class="course-credits" data-label="Credits">3.0</td>
            </tr>
          </tbody>
        </table>
        <div class="term-summary">Term GPA: 3.71 • Total Credits: 17.0 • Cumulative GPA: 3.64</div>
      </div>
      
      <div class="term-section">
        <div class="term-header">Fall Semester 2024 - Second Year</div>
        <table class="courses-table">
          <thead>
            <tr>
              <th>Course Code</th>
              <th>Course Title</th>
              <th>Grade</th>
              <th>Credits</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="course-code" data-label="Course Code">CS 25000</td>
              <td data-label="Course Title">Computer Architecture</td>
              <td class="course-grade" data-label="Grade">A</td>
              <td class="course-credits" data-label="Credits">4.0</td>
            </tr>
            <tr>
              <td class="course-code" data-label="Course Code">CS 25100</td>
              <td data-label="Course Title">Data Structures and Algorithms</td>
              <td class="course-grade" data-label="Grade">A-</td>
              <td class="course-credits" data-label="Credits">3.0</td>
            </tr>
            <tr>
              <td class="course-code" data-label="Course Code">MA 26100</td>
              <td data-label="Course Title">Multivariate Calculus</td>
              <td class="course-grade" data-label="Grade">B+</td>
              <td class="course-credits" data-label="Credits">4.0</td>
            </tr>
            <tr>
              <td class="course-code" data-label="Course Code">STAT 35000</td>
              <td data-label="Course Title">Introduction to Statistics</td>
              <td class="course-grade" data-label="Grade">A</td>
              <td class="course-credits" data-label="Credits">3.0</td>
            </tr>
            <tr>
              <td class="course-code" data-label="Course Code">HIST 15200</td>
              <td data-label="Course Title">Science in the Modern World</td>
              <td class="course-grade" data-label="Grade">B</td>
              <td class="course-credits" data-label="Credits">3.0</td>
            </tr>
          </tbody>
        </table>
        <div class="term-summary">Term GPA: 3.71 • Total Credits: 17.0 • Cumulative GPA: 3.66</div>
      </div>
      
      <div class="term-section current-term">
        <div class="term-header">Spring Semester 2025 - Second Year (Current)</div>
        <table class="courses-table">
          <thead>
            <tr>
              <th>Course Code</th>
              <th>Course Title</th>
              <th>Grade</th>
              <th>Credits</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="course-code" data-label="Course Code">CS 24000</td>
              <td data-label="Course Title">Programming in C</td>
              <td class="course-grade" data-label="Grade">A</td>
              <td class="course-credits" data-label="Credits">3.0</td>
            </tr>
            <tr>
              <td class="course-code" data-label="Course Code">CS 25200</td>
              <td data-label="Course Title">Systems Programming</td>
              <td class="course-grade" data-label="Grade">A-</td>
              <td class="course-credits" data-label="Credits">4.0</td>
            </tr>
            <tr>
              <td class="course-code" data-label="Course Code">MA 35100</td>
              <td data-label="Course Title">Elementary Linear Algebra</td>
              <td class="course-grade" data-label="Grade">B+</td>
              <td class="course-credits" data-label="Credits">3.0</td>
            </tr>
            <tr>
              <td class="course-code" data-label="Course Code">CS 18200</td>
              <td data-label="Course Title">Discrete Mathematics</td>
              <td class="course-grade" data-label="Grade">A</td>
              <td class="course-credits" data-label="Credits">3.0</td>
            </tr>
            <tr>
              <td class="course-code" data-label="Course Code">PHIL 11000</td>
              <td data-label="Course Title">Introduction to Philosophy</td>
              <td class="course-grade" data-label="Grade">B+</td>
              <td class="course-credits" data-label="Credits">3.0</td>
            </tr>
          </tbody>
        </table>
        <div class="term-summary">Term GPA: 3.63 • Total Credits: 16.0 • Cumulative GPA: 3.65</div>
      </div>
    </div>
  </div>
</body>
</html>