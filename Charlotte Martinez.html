<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Unofficial Transcript - University of California, Berkeley</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      font-size: 12px;
      line-height: 1.4;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
      color: #333;
    }
    
    .transcript {
      max-width: 8.5in;
      margin: 0 auto;
      background: white;
      padding: 30px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .header {
      text-align: center;
      border-bottom: 2px solid #003262;
      padding-bottom: 15px;
      margin-bottom: 20px;
      color: #003262;
    }
    
    .header h1 {
      font-size: 18px;
      margin: 0 0 5px 0;
      font-weight: bold;
    }
    
    .header .address {
      font-size: 11px;
      margin: 2px 0;
    }
    
    .transcript-title {
      font-size: 16px;
      font-weight: bold;
      text-align: center;
      margin: 15px 0;
      text-transform: uppercase;
      color: #003262;
    }
    
    .print-info {
      text-align: right;
      font-size: 11px;
      margin-bottom: 20px;
    }
    
    .student-info {
      margin-bottom: 20px;
    }
    
    .info-row {
      display: flex;
      margin-bottom: 3px;
    }
    
    .info-label {
      font-weight: bold;
      width: 140px;
    }
    
    .summary-section {
      display: flex;
      justify-content: space-between;
      margin: 20px 0;
      gap: 20px;
    }
    
    .summary-box {
      flex: 1;
      border: 1px solid #ccc;
      padding: 10px;
      background-color: #f9f9f9;
    }
    
    .summary-box h3 {
      margin: 0 0 8px 0;
      font-size: 12px;
      font-weight: bold;
      color: #003262;
    }
    
    .summary-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 3px;
      font-size: 11px;
    }
    
    .term-section {
      margin: 25px 0;
    }
    
    .term-title {
      background-color: #003262;
      color: white;
      padding: 8px 10px;
      font-weight: bold;
      font-size: 13px;
      margin-bottom: 0;
    }
    
    .courses-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 5px;
    }
    
    .courses-table th {
      background-color: #f0f0f0;
      border: 1px solid #ccc;
      padding: 6px 8px;
      text-align: left;
      font-weight: bold;
      font-size: 11px;
    }
    
    .courses-table td {
      border: 1px solid #ccc;
      padding: 6px 8px;
      font-size: 11px;
    }
    
    .term-summary {
      background-color: #e8f4f8;
      padding: 6px 10px;
      font-weight: bold;
      font-size: 11px;
      text-align: center;
    }
    
    @media print {
      body { background: white; padding: 0; }
      .transcript { box-shadow: none; }
    }
  </style>
</head>
<body>
  <div class="transcript">
    <div class="header">
      <h1>University of California, Berkeley</h1>
      <div class="address">Berkeley, CA 94720</div>
      <div class="address">Phone: (*************</div>
    </div>
    
    <div class="print-info">
      <div>Print Date: 06/15/2025</div>
      <div><strong>UNOFFICIAL TRANSCRIPT</strong></div>
    </div>
    
    <div class="transcript-title">Student Academic Record</div>
    
    <div class="student-info">
      <div class="info-row">
        <span class="info-label">Student Name:</span>
        <span>Charlotte Martinez</span>
      </div>
      <div class="info-row">
        <span class="info-label">Student ID:</span>
        <span>3039875412</span>
      </div>
      <div class="info-row">
        <span class="info-label">Date of Birth:</span>
        <span>09/10/2005</span>
      </div>
      <div class="info-row">
        <span class="info-label">Address:</span>
        <span>2847 Channing Way, Berkeley, CA 94704</span>
      </div>
      <div class="info-row">
        <span class="info-label">College:</span>
        <span>College of Letters & Science</span>
      </div>
      <div class="info-row">
        <span class="info-label">Major:</span>
        <span>Computer Science</span>
      </div>
      <div class="info-row">
        <span class="info-label">Degree Sought:</span>
        <span>Bachelor of Arts</span>
      </div>
    </div>
    
    <div class="summary-section">
      <div class="summary-box">
        <h3>Academic Summary</h3>
        <div class="summary-item">
          <span>Cumulative GPA:</span>
          <span>3.68</span>
        </div>
        <div class="summary-item">
          <span>Total Credits Attempted:</span>
          <span>64.0</span>
        </div>
        <div class="summary-item">
          <span>Total Credits Earned:</span>
          <span>64.0</span>
        </div>
        <div class="summary-item">
          <span>Credits in Progress:</span>
          <span>12.0</span>
        </div>
      </div>
      <div class="summary-box">
        <h3>Academic Status</h3>
        <div class="summary-item">
          <span>Academic Standing:</span>
          <span>Good Standing</span>
        </div>
        <div class="summary-item">
          <span>Classification:</span>
          <span>Sophomore</span>
        </div>
        <div class="summary-item">
          <span>Enrollment Date:</span>
          <span>08/2023</span>
        </div>
        <div class="summary-item">
          <span>Expected Graduation:</span>
          <span>05/2027</span>
        </div>
      </div>
    </div>
    
    <div class="term-section">
      <div class="term-title">Fall Semester 2023</div>
      <table class="courses-table">
        <tr>
          <th>Course</th>
          <th>Title</th>
          <th>Grade</th>
          <th>Credits</th>
        </tr>
        <tr>
          <td>CS 61A</td>
          <td>The Structure and Interpretation of Computer Programs</td>
          <td>A-</td>
          <td>4.0</td>
        </tr>
        <tr>
          <td>MATH 1A</td>
          <td>Calculus</td>
          <td>B+</td>
          <td>4.0</td>
        </tr>
        <tr>
          <td>CHEM 1A</td>
          <td>General Chemistry</td>
          <td>B</td>
          <td>4.0</td>
        </tr>
        <tr>
          <td>COLWRIT R4A</td>
          <td>Reading and Composition</td>
          <td>A-</td>
          <td>4.0</td>
        </tr>
      </table>
      <div class="term-summary">Term GPA: 3.56 • Credits: 16.0</div>
    </div>
    
    <div class="term-section">
      <div class="term-title">Spring Semester 2024</div>
      <table class="courses-table">
        <tr>
          <th>Course</th>
          <th>Title</th>
          <th>Grade</th>
          <th>Credits</th>
        </tr>
        <tr>
          <td>CS 61B</td>
          <td>Data Structures</td>
          <td>A</td>
          <td>4.0</td>
        </tr>
        <tr>
          <td>MATH 1B</td>
          <td>Calculus</td>
          <td>B+</td>
          <td>4.0</td>
        </tr>
        <tr>
          <td>MATH 54</td>
          <td>Linear Algebra and Differential Equations</td>
          <td>B</td>
          <td>4.0</td>
        </tr>
        <tr>
          <td>COLWRIT R4B</td>
          <td>Reading and Composition</td>
          <td>A-</td>
          <td>4.0</td>
        </tr>
      </table>
      <div class="term-summary">Term GPA: 3.56 • Credits: 16.0</div>
    </div>
    
    <div class="term-section">
      <div class="term-title">Fall Semester 2024</div>
      <table class="courses-table">
        <tr>
          <th>Course</th>
          <th>Title</th>
          <th>Grade</th>
          <th>Credits</th>
        </tr>
        <tr>
          <td>CS 61C</td>
          <td>Machine Structures</td>
          <td>A-</td>
          <td>4.0</td>
        </tr>
        <tr>
          <td>CS 70</td>
          <td>Discrete Mathematics and Probability Theory</td>
          <td>B+</td>
          <td>4.0</td>
        </tr>
        <tr>
          <td>STAT 20</td>
          <td>Introduction to Probability and Statistics</td>
          <td>A</td>
          <td>4.0</td>
        </tr>
        <tr>
          <td>PHYSICS 7A</td>
          <td>Physics for Scientists and Engineers</td>
          <td>B-</td>
          <td>4.0</td>
        </tr>
      </table>
      <div class="term-summary">Term GPA: 3.56 • Credits: 16.0</div>
    </div>
    
    <div class="term-section">
      <div class="term-title">Spring Semester 2025</div>
      <table class="courses-table">
        <tr>
          <th>Course</th>
          <th>Title</th>
          <th>Grade</th>
          <th>Credits</th>
        </tr>
        <tr>
          <td>CS 170</td>
          <td>Efficient Algorithms and Intractable Problems</td>
          <td>A</td>
          <td>4.0</td>
        </tr>
        <tr>
          <td>CS 188</td>
          <td>Introduction to Artificial Intelligence</td>
          <td>B+</td>
          <td>4.0</td>
        </tr>
        <tr>
          <td>PHYSICS 7B</td>
          <td>Physics for Scientists and Engineers</td>
          <td>B</td>
          <td>4.0</td>
        </tr>
        <tr>
          <td>ECON 1</td>
          <td>Introduction to Economics</td>
          <td>A-</td>
          <td>4.0</td>
        </tr>
      </table>
      <div class="term-summary">Term GPA: 3.75 • Credits: 16.0</div>
    </div>
    
    <div class="term-section">
      <div class="term-title">Summer Session 2025 (In Progress)</div>
      <table class="courses-table">
        <tr>
          <th>Course</th>
          <th>Title</th>
          <th>Grade</th>
          <th>Credits</th>
        </tr>
        <tr>
          <td>CS 162</td>
          <td>Operating Systems and System Programming</td>
          <td>IP</td>
          <td>4.0</td>
        </tr>
        <tr>
          <td>CS 186</td>
          <td>Introduction to Database Systems</td>
          <td>IP</td>
          <td>4.0</td>
        </tr>
        <tr>
          <td>SOCIOL 1</td>
          <td>Introduction to Sociology</td>
          <td>IP</td>
          <td>4.0</td>
        </tr>
      </table>
      <div class="term-summary">Current Enrollment: 12.0 Credits</div>
    </div>
  </div>
</body>
</html>