<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Unofficial Transcript - Irvinedale High School</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Merriweather:wght@700&display=swap');

    :root {
      --primary: #2A4365;
      --secondary: #38B2AC;
      --accent: #ED8936;
      --light-bg: #F7FAFC;
      --medium-bg: #EDF2F7;
      --dark-bg: #E2E8F0;
      --text-dark: #2D3748;
      --text-medium: #4A5568;
      --text-light: #718096;
      --border-radius: 8px;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Montserrat', sans-serif;
      font-size: 12px;
      line-height: 1.6;
      color: var(--text-dark);
      background-color: #E2E8F0;
      padding: 25px;
    }

    .container {
      max-width: 210mm;
      margin: 0 auto;
      background: white;
      box-shadow: 0 4px 20px rgba(0,0,0,0.08);
      position: relative;
      border-radius: var(--border-radius);
      overflow: hidden;
    }

    .header {
      display: grid;
      grid-template-columns: auto 1fr auto;
      align-items: center;
      padding: 20px 30px;
      background-color: var(--primary);
      color: white;
      position: relative;
    }

    .header::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(to right, var(--secondary), var(--accent));
    }

    .logo {
      display: flex;
      flex-direction: column;
      gap: 6px;
    }

    .school-name {
      font-family: 'Merriweather', serif;
      font-size: 26px;
      font-weight: 700;
      letter-spacing: 0.5px;
    }

    .school-info {
      font-size: 10px;
      line-height: 1.4;
      opacity: 0.9;
    }

    .document-title {
      text-align: right;
      font-size: 20px;
      font-weight: 700;
      padding-right: 20px;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .document-title span {
      color: var(--accent);
    }

    .print-info {
      text-align: right;
      font-size: 10px;
      padding: 10px 30px;
      background-color: var(--light-bg);
      border-bottom: 1px solid var(--dark-bg);
    }

    .student-section {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30px;
      padding: 25px 30px;
      background-color: var(--medium-bg);
      border-bottom: 2px solid var(--dark-bg);
    }

    .section-title {
      color: var(--primary);
      font-weight: 700;
      text-transform: uppercase;
      font-size: 14px;
      margin-bottom: 15px;
      font-family: 'Merriweather', serif;
      letter-spacing: 0.5px;
      border-bottom: 3px solid var(--secondary);
      padding-bottom: 5px;
      display: inline-block;
    }

    .student-info {
      display: grid;
      grid-template-columns: auto 1fr;
      gap: 10px 20px;
    }

    .info-label {
      font-weight: 600;
      color: var(--text-medium);
    }

    .summary-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 10px;
    }

    .summary-item {
      display: flex;
      justify-content: space-between;
      padding: 8px 12px;
      background-color: white;
      border-radius: var(--border-radius);
      box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }

    .summary-label {
      font-weight: 600;
      color: var(--text-medium);
    }

    .academic-record {
      padding: 25px 30px;
      background-color: white;
    }

    .semester {
      margin-bottom: 30px;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }

    .semester-header {
      background: linear-gradient(to right, var(--primary), #3C5C8A);
      color: white;
      padding: 10px 15px;
      font-weight: 600;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .courses-table {
      width: 100%;
      border-collapse: collapse;
    }

    .courses-table th {
      background-color: var(--medium-bg);
      font-weight: 600;
      text-align: left;
      padding: 10px 15px;
      color: var(--text-medium);
    }

    .courses-table td {
      padding: 8px 15px;
      border-bottom: 1px solid var(--dark-bg);
    }

    .courses-table tr:last-child td {
      border-bottom: none;
    }

    .courses-table tr:nth-child(even) {
      background-color: var(--light-bg);
    }

    .course-code {
      font-weight: 600;
      color: var(--primary);
    }

    .semester-summary {
      display: flex;
      justify-content: space-between;
      padding: 10px 15px;
      background-color: var(--medium-bg);
      font-weight: 600;
      color: var(--text-medium);
    }

    .grade-a {
      color: #2F855A;
      font-weight: 600;
    }

    .grade-b {
      color: #3182CE;
      font-weight: 600;
    }

    .grade-c {
      color: #DD6B20;
      font-weight: 600;
    }

    .grade-d {
      color: #C53030;
      font-weight: 600;
    }

    .footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 30px;
      background-color: var(--primary);
      color: white;
      font-size: 10px;
      position: relative;
    }

    .footer::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(to right, var(--secondary), var(--accent));
    }

    .watermark {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) rotate(-45deg);
      font-size: 100px;
      font-weight: 700;
      color: rgba(0,0,0,0.03);
      white-space: nowrap;
      pointer-events: none;
      z-index: 10;
    }

    @media print {
      body {
        padding: 0;
        background: none;
      }
      
      .container {
        box-shadow: none;
        border-radius: 0;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="watermark">UNOFFICIAL</div>
    
    <header class="header">
      <div class="logo">
        <div class="school-name">Irvinedale High School</div>
        <div class="school-info">
          1250 Westwood Blvd<br>
          Irvine, CA 92618<br>
          (949) 555-7890
        </div>
      </div>
      <div></div>
      <div class="document-title"></div>
    </header>
    
    <div class="print-info">
      <div><strong>Print Date:</strong> 07/05/2024 | <strong>CEEB Code:</strong> 051872 | <strong>Unofficial Transcript</strong></div>
    </div>
    
    <section class="student-section">
      <div>
        <div class="section-title">Student Information</div>
        <div class="student-info">
          <div class="info-label">Name:</div>
          <div>Harper Ward</div>
          
          <div class="info-label">Student ID:</div>
          <div>1847293051</div>
          
          <div class="info-label">Date of Birth:</div>
          <div>June 8, 2005</div>
          
          <div class="info-label">Graduation Date:</div>
          <div>June 5, 2024</div>
          
          <div class="info-label">Address:</div>
          <div>2841 Oak Street, Irvine, CA 92614</div>
          
          <div class="info-label">Diploma:</div>
          <div>High School Diploma</div>
        </div>
      </div>
      
      <div>
        <div class="section-title">Academic Summary</div>
        <div class="summary-grid">
          <div class="summary-item">
            <span class="summary-label">Cumulative GPA:</span>
            <span>3.50</span>
          </div>
          
          <div class="summary-item">
            <span class="summary-label">Class Rank:</span>
            <span>125 of 368</span>
          </div>
          
          <div class="summary-item">
            <span class="summary-label">Total Credits:</span>
            <span>26.0</span>
          </div>
          
          <div class="summary-item">
            <span class="summary-label">Honors/Awards:</span>
            <span>Honor Roll</span>
          </div>
          
          <div class="summary-item">
            <span class="summary-label">Graduation Status:</span>
            <span>Graduated</span>
          </div>
          
          <div class="summary-item">
            <span class="summary-label">Years Attended:</span>
            <span>2020-2024</span>
          </div>
        </div>
      </div>
    </section>
    
    <section class="academic-record">
      <div class="section-title">Academic Record</div>
      
      <div class="semester">
        <div class="semester-header">
          <span>Freshman Year (2020-2021)</span>
          <span>Year GPA: 3.25</span>
        </div>
        <table class="courses-table">
          <tr>
            <th width="15%">Course ID</th>
            <th width="50%">Course Title</th>
            <th width="15%">Grade</th>
            <th width="20%">Credit Hours</th>
          </tr>
          <tr>
            <td class="course-code">ENG9</td>
            <td>English 9</td>
            <td class="grade-a">A-</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">ALG1</td>
            <td>Algebra I</td>
            <td class="grade-b">B+</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">BIO</td>
            <td>Biology</td>
            <td class="grade-a">A</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">WH</td>
            <td>World History</td>
            <td class="grade-b">B</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">SPN1</td>
            <td>Spanish I</td>
            <td class="grade-b">B+</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">PE9</td>
            <td>Physical Education</td>
            <td class="grade-a">A</td>
            <td>0.5</td>
          </tr>
          <tr>
            <td class="course-code">ART</td>
            <td>Art I</td>
            <td class="grade-a">A</td>
            <td>0.5</td>
          </tr>
        </table>
        <div class="semester-summary">
          <span>Total Credits: 6.0</span>
          <span>Year GPA: 3.25</span>
        </div>
      </div>
      
      <div class="semester">
        <div class="semester-header">
          <span>Sophomore Year (2021-2022)</span>
          <span>Year GPA: 3.42</span>
        </div>
        <table class="courses-table">
          <tr>
            <th width="15%">Course ID</th>
            <th width="50%">Course Title</th>
            <th width="15%">Grade</th>
            <th width="20%">Credit Hours</th>
          </tr>
          <tr>
            <td class="course-code">ENG10</td>
            <td>English 10</td>
            <td class="grade-a">A</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">GEO</td>
            <td>Geometry</td>
            <td class="grade-b">B+</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">CHEM</td>
            <td>Chemistry</td>
            <td class="grade-a">A-</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">USH</td>
            <td>U.S. History</td>
            <td class="grade-b">B+</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">SPN2</td>
            <td>Spanish II</td>
            <td class="grade-b">B</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">PE10</td>
            <td>Physical Education</td>
            <td class="grade-a">A</td>
            <td>0.5</td>
          </tr>
          <tr>
            <td class="course-code">MUS</td>
            <td>Concert Band</td>
            <td class="grade-a">A</td>
            <td>0.5</td>
          </tr>
        </table>
        <div class="semester-summary">
          <span>Total Credits: 6.0</span>
          <span>Year GPA: 3.42</span>
        </div>
      </div>

      <div class="semester">
        <div class="semester-header">
          <span>Junior Year (2022-2023)</span>
          <span>Year GPA: 3.58</span>
        </div>
        <table class="courses-table">
          <tr>
            <th width="15%">Course ID</th>
            <th width="50%">Course Title</th>
            <th width="15%">Grade</th>
            <th width="20%">Credit Hours</th>
          </tr>
          <tr>
            <td class="course-code">ENG11</td>
            <td>English 11</td>
            <td class="grade-a">A-</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">ALG2</td>
            <td>Algebra II</td>
            <td class="grade-b">B+</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">PHYS</td>
            <td>Physics</td>
            <td class="grade-a">A</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">APGOV</td>
            <td>AP Government</td>
            <td class="grade-a">A-</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">SPN3</td>
            <td>Spanish III</td>
            <td class="grade-b">B+</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">HLTH</td>
            <td>Health</td>
            <td class="grade-a">A</td>
            <td>0.5</td>
          </tr>
          <tr>
            <td class="course-code">COMP</td>
            <td>Computer Applications</td>
            <td class="grade-a">A</td>
            <td>0.5</td>
          </tr>
        </table>
        <div class="semester-summary">
          <span>Total Credits: 6.0</span>
          <span>Year GPA: 3.58</span>
        </div>
      </div>

      <div class="semester">
        <div class="semester-header">
          <span>Senior Year (2023-2024)</span>
          <span>Year GPA: 3.75</span>
        </div>
        <table class="courses-table">
          <tr>
            <th width="15%">Course ID</th>
            <th width="50%">Course Title</th>
            <th width="15%">Grade</th>
            <th width="20%">Credit Hours</th>
          </tr>
          <tr>
            <td class="course-code">APENG</td>
            <td>AP English Literature</td>
            <td class="grade-a">A</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">PRECAL</td>
            <td>Pre-Calculus</td>
            <td class="grade-a">A-</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">APES</td>
            <td>AP Environmental Science</td>
            <td class="grade-a">A</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">ECON</td>
            <td>Economics</td>
            <td class="grade-b">B+</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">SPN4</td>
            <td>Spanish IV</td>
            <td class="grade-a">A-</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">PSYC</td>
            <td>Psychology</td>
            <td class="grade-a">A</td>
            <td>1.0</td>
          </tr>
          <tr>
            <td class="course-code">SENT</td>
            <td>Senior Tutorial</td>
            <td class="grade-a">A</td>
            <td>2.0</td>
          </tr>
        </table>
        <div class="semester-summary">
          <span>Total Credits: 8.0</span>
          <span>Year GPA: 3.75</span>
        </div>
      </div>
    </section>

    <footer class="footer">
      <div>© 2025 Irvinedale High School. All rights reserved.</div>
      <div>This transcript reflects 26.0 credits required for graduation.</div>
    </footer>
  </div>
</body>
</html>
