<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Academic Transcript - <PERSON></title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: Arial, sans-serif;
      background: #f8f9fa;
      padding: 20px;
      color: #333;
      line-height: 1.5;
    }
    
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .header {
      text-align: center;
      border-bottom: 2px solid #ddd;
      padding-bottom: 20px;
      margin-bottom: 30px;
    }
    
    .university {
      font-size: 20px;
      font-weight: bold;
      color: #2c3e50;
      margin-bottom: 5px;
    }
    
    .location {
      color: #666;
      margin-bottom: 10px;
    }
    
    .transcript-title {
      font-size: 14px;
      text-transform: uppercase;
      color: #7f8c8d;
      letter-spacing: 1px;
    }
    
    .student-info {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30px;
      margin-bottom: 30px;
    }
    
    .info-section h3 {
      font-size: 16px;
      color: #2c3e50;
      margin-bottom: 15px;
      border-bottom: 1px solid #eee;
      padding-bottom: 5px;
    }
    
    .info-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      font-size: 14px;
    }
    
    .label {
      color: #666;
      font-weight: 500;
    }
    
    .value {
      font-weight: 600;
      color: #2c3e50;
    }
    
    .gpa-box {
      background: #f1f3f5;
      padding: 15px;
      border-radius: 6px;
      text-align: center;
      margin-top: 15px;
    }
    
    .gpa-number {
      font-size: 24px;
      font-weight: bold;
      color: #2c3e50;
    }
    
    .gpa-label {
      font-size: 12px;
      color: #666;
      text-transform: uppercase;
    }
    
    .courses {
      margin-top: 30px;
    }
    
    .courses h3 {
      font-size: 16px;
      color: #2c3e50;
      margin-bottom: 20px;
      border-bottom: 1px solid #eee;
      padding-bottom: 5px;
    }
    
    .semester {
      margin-bottom: 25px;
    }
    
    .semester-title {
      background: #2c3e50;
      color: white;
      padding: 8px 15px;
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 10px;
    }
    
    .course-list {
      border: 1px solid #ddd;
    }
    
    .course {
      display: grid;
      grid-template-columns: 1fr 3fr auto auto;
      gap: 15px;
      padding: 12px 15px;
      border-bottom: 1px solid #eee;
      align-items: center;
    }
    
    .course:last-child {
      border-bottom: none;
    }
    
    .course-code {
      font-family: monospace;
      font-weight: bold;
      font-size: 13px;
      color: #2c3e50;
    }
    
    .course-title {
      font-size: 14px;
      color: #333;
    }
    
    .grade {
      font-weight: bold;
      text-align: center;
      padding: 4px 8px;
      border-radius: 3px;
      font-size: 12px;
      min-width: 35px;
    }
    
    .grade.a { background: #d4edda; color: #155724; }
    .grade.a-minus { background: #d1ecf1; color: #0c5460; }
    .grade.b-plus { background: #fff3cd; color: #856404; }
    .grade.ip { background: #f8d7da; color: #721c24; }
    
    .credits {
      text-align: center;
      font-weight: 600;
      color: #495057;
    }
    
    .semester-summary {
      background: #f8f9fa;
      padding: 10px 15px;
      text-align: right;
      font-size: 12px;
      color: #495057;
      font-weight: 600;
    }
    
    .footer {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #ddd;
      text-align: center;
      font-size: 11px;
      color: #666;
    }
    
    @media (max-width: 768px) {
      .student-info {
        grid-template-columns: 1fr;
        gap: 20px;
      }
      
      .course {
        grid-template-columns: 1fr;
        gap: 5px;
        text-align: center;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="university">Florida State University</div>
      <div class="location">Tallahassee, FL 32306</div>
      <div class="transcript-title">Unofficial Academic Transcript</div>
    </div>
    
    <div class="student-info">
      <div class="info-section">
        <h3>Student Information</h3>
        <div class="info-row">
          <span class="label">Name</span>
          <span class="value">Sebastian Levi</span>
        </div>
        <div class="info-row">
          <span class="label">Student ID</span>
          <span class="value">900458721</span>
        </div>
        <div class="info-row">
          <span class="label">Date of Birth</span>
          <span class="value">May 22, 2005</span>
        </div>
        <div class="info-row">
          <span class="label">Email</span>
          <span class="value"><EMAIL></span>
        </div>
      </div>
      
      <div class="info-section">
        <h3>Program Information</h3>
        <div class="info-row">
          <span class="label">Major</span>
          <span class="value">Physics</span>
        </div>
        <div class="info-row">
          <span class="label">Minor</span>
          <span class="value">Mathematics</span>
        </div>
        <div class="info-row">
          <span class="label">Degree</span>
          <span class="value">Bachelor of Science</span>
        </div>
        <div class="info-row">
          <span class="label">Expected Graduation</span>
          <span class="value">Spring 2027</span>
        </div>
        
        <div class="gpa-box">
          <div class="gpa-number">3.71</div>
          <div class="gpa-label">Cumulative GPA</div>
        </div>
      </div>
    </div>
    
    <div class="courses">
      <h3>Academic History</h3>
      
      <div class="semester">
        <div class="semester-title">Fall 2023</div>
        <div class="course-list">
          <div class="course">
            <span class="course-code">PHY 2048C</span>
            <span class="course-title">Physics with Calculus I</span>
            <span class="grade a-minus">A-</span>
            <span class="credits">4</span>
          </div>
          <div class="course">
            <span class="course-code">MAC 2311</span>
            <span class="course-title">Calculus I</span>
            <span class="grade b-plus">B+</span>
            <span class="credits">4</span>
          </div>
          <div class="course">
            <span class="course-code">CHM 1045</span>
            <span class="course-title">General Chemistry I</span>
            <span class="grade b-plus">B+</span>
            <span class="credits">3</span>
          </div>
          <div class="course">
            <span class="course-code">ENC 1101</span>
            <span class="course-title">Freshman Composition</span>
            <span class="grade a">A</span>
            <span class="credits">3</span>
          </div>
        </div>
        <div class="semester-summary">GPA: 3.57 | Credits: 14</div>
      </div>
      
      <div class="semester">
        <div class="semester-title">Spring 2024</div>
        <div class="course-list">
          <div class="course">
            <span class="course-code">PHY 2049C</span>
            <span class="course-title">Physics with Calculus II</span>
            <span class="grade a">A</span>
            <span class="credits">4</span>
          </div>
          <div class="course">
            <span class="course-code">MAC 2312</span>
            <span class="course-title">Calculus II</span>
            <span class="grade b-plus">B+</span>
            <span class="credits">4</span>
          </div>
          <div class="course">
            <span class="course-code">CHM 1046</span>
            <span class="course-title">General Chemistry II</span>
            <span class="grade a-minus">A-</span>
            <span class="credits">3</span>
          </div>
          <div class="course">
            <span class="course-code">STA 2023</span>
            <span class="course-title">Elementary Statistics</span>
            <span class="grade a">A</span>
            <span class="credits">3</span>
          </div>
        </div>
        <div class="semester-summary">GPA: 3.74 | Credits: 14</div>
      </div>
      
      <div class="semester">
        <div class="semester-title">Fall 2024</div>
        <div class="course-list">
          <div class="course">
            <span class="course-code">PHY 3101</span>
            <span class="course-title">Modern Physics</span>
            <span class="grade a">A</span>
            <span class="credits">3</span>
          </div>
          <div class="course">
            <span class="course-code">MAC 2313</span>
            <span class="course-title">Calculus III</span>
            <span class="grade a-minus">A-</span>
            <span class="credits">4</span>
          </div>
          <div class="course">
            <span class="course-code">PHY 3221</span>
            <span class="course-title">Mechanics I</span>
            <span class="grade b-plus">B+</span>
            <span class="credits">3</span>
          </div>
          <div class="course">
            <span class="course-code">MAD 2104</span>
            <span class="course-title">Discrete Mathematics</span>
            <span class="grade a">A</span>
            <span class="credits">3</span>
          </div>
          <div class="course">
            <span class="course-code">PHI 2010</span>
            <span class="course-title">Introduction to Philosophy</span>
            <span class="grade a-minus">A-</span>
            <span class="credits">3</span>
          </div>
        </div>
        <div class="semester-summary">GPA: 3.73 | Credits: 16</div>
      </div>
      
      <div class="semester">
        <div class="semester-title">Spring 2025</div>
        <div class="course-list">
          <div class="course">
            <span class="course-code">PHY 3424</span>
            <span class="course-title">Electromagnetism I</span>
            <span class="grade a">A</span>
            <span class="credits">3</span>
          </div>
          <div class="course">
            <span class="course-code">MAP 2302</span>
            <span class="course-title">Differential Equations</span>
            <span class="grade a-minus">A-</span>
            <span class="credits">3</span>
          </div>
          <div class="course">
            <span class="course-code">PHY 3513</span>
            <span class="course-title">Thermal Physics</span>
            <span class="grade b-plus">B+</span>
            <span class="credits">3</span>
          </div>
          <div class="course">
            <span class="course-code">MAS 3114</span>
            <span class="course-title">Linear Algebra</span>
            <span class="grade a">A</span>
            <span class="credits">3</span>
          </div>
          <div class="course">
            <span class="course-code">PSY 2012</span>
            <span class="course-title">General Psychology</span>
            <span class="grade a">A</span>
            <span class="credits">3</span>
          </div>
        </div>
        <div class="semester-summary">GPA: 3.80 | Credits: 15</div>
      </div>
      
      <div class="semester">
        <div class="semester-title">Summer 2025</div>
        <div class="course-list">
          <div class="course">
            <span class="course-code">PHY 2053L</span>
            <span class="course-title">College Physics Laboratory</span>
            <span class="grade a">A</span>
            <span class="credits">1</span>
          </div>
          <div class="course">
            <span class="course-code">MAA 4211</span>
            <span class="course-title">Advanced Calculus I</span>
            <span class="grade a-minus">A-</span>
            <span class="credits">3</span>
          </div>
        </div>
        <div class="semester-summary">GPA: 3.75 | Credits: 4</div>
      </div>
    </div>
    
    <div class="footer">
      Document ID: FSU-TR-2025-SL-1847 | Issue Date: June 15, 2025<br>
      This is an unofficial transcript. For official records, contact FSU Registrar's Office.
    </div>
  </div>
</body>
</html>